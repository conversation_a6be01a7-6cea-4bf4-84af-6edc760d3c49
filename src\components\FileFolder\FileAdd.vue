<template>
  <van-floating-bubble
    v-model:offset="offset"
    icon="plus"
    @touchend.prevent="onClick"
    @offset-change="onOffsetChange"
  />
  <van-action-sheet
    v-model:show="show"
    :actions="sheetActions"
    cancel-text="取消"
    close-on-click-action
    @cancel="onCancel"
    @select="onSelect"
  >
    <div v-if="showFreeTrialWarning">
      <h4 class="trial-warning">
        <div></div>
        <div v-if="profileStore.freeTrialCount > 0">
          剩余试用次数：
          <el-text type="danger">
            {{ profileStore.freeTrialCount }}
          </el-text>
        </div>
        <div v-else>
          <el-text type="danger">试用已结束</el-text>
        </div>
        <div>
          <el-button
            size="small"
            link
            type="danger"
            @click="gotoMemberInfoPage"
          >
            开通会员
          </el-button>
        </div>
      </h4>
    </div>
  </van-action-sheet>
  <input
    ref="pdfInputRef"
    type="file"
    accept="application/pdf"
    @change="uploadFileChange"
    style="display: none"
  />
  <input
    ref="cameraInputRef"
    type="file"
    accept="image/*"
    capture="environment"
    @change="takePhotoChange"
    style="display: none"
  />
  <input
    ref="photoInputRef"
    type="file"
    accept="image/*"
    multiple
    @change="uploadPictureChange"
    style="display: none"
  />
</template>

<script setup>
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import {
  useDebounceFn,
  useEventListener,
  useResizeObserver,
} from "@vueuse/core";
import * as pdfjsLib from "pdfjs-dist";
import { useAppStore } from "@/stores/app.js";
import { useProfileStore } from "@/stores/profile";
import {
  createNewFileMeta,
  putFileMeta,
  updateParentFolderFileCount,
} from "@/lib/FileList.js";
import { asyncReadAsDataURL } from "@/lib/FileCover.js";
import { saveOcrData } from "@/lib/RectDatabase.js";
import { showAskFilenameDialog } from "@/components/FileFolder/Helper/AskFilenameDialog.jsx";

const router = useRouter();
const appStore = useAppStore();
const profileStore = useProfileStore();

const pdfInputRef = ref();
const cameraInputRef = ref();
const photoInputRef = ref();

const offset = ref({ x: window.innerWidth - 70, y: window.innerHeight - 150 });
const show = ref(false);
let isOffsetChange = false;
const actions = [
  // { name: "新建笔记" },
  { name: "新建文件夹" },
  { name: "导入PDF" },
  { name: "拍照" },
  { name: "相册" },
];

const actionsLock = [
  // { name: "新建笔记", icon: "lock" },
  { name: "新建文件夹" },
  { name: "导入PDF", icon: "lock" },
  { name: "拍照", icon: "lock" },
  { name: "相册", icon: "lock" },
];

const sheetActions = computed(() => {
  if (!profileStore.haveTrialPermission) {
    return actionsLock;
  } else {
    return actions;
  }
});

const showFreeTrialWarning = computed(() => {
  return (
    !profileStore.isVIP &&
    profileStore.freeTrialCount >= 0 &&
    profileStore.freeTrialCount <= 3
  );
});

const resizeCallback = useDebounceFn(() => {
  offset.value = { x: window.innerWidth - 70, y: window.innerHeight - 150 };
}, 100);
useResizeObserver(document.body, resizeCallback);

useEventListener("message", (event) => {
  if (event?.data?.action === "expo-take-photo") {
    takePhoto();
  }
});

const onCancel = () => {
  show.value = false;
};

function onOffsetChange() {
  isOffsetChange = true;
}

const onClick = useDebounceFn(() => {
  if (isOffsetChange) {
    isOffsetChange = false;
    return;
  }
  show.value = true;
}, 50);

function checkPermission() {
  if (!profileStore.haveTrialPermission) {
    ElMessage.warning("试用已结束，请升级会员");
    return false;
  }
  return true;
}

function gotoMemberInfoPage() {
  router.push("/profile/member-info");
}

function onSelect(item) {
  if (!checkPermission()) {
    return;
  }

  if (item.name === "新建笔记") {
    createNote();
  } else if (item.name === "新建文件夹") {
    createFolder();
  } else if (item.name === "导入PDF") {
    uploadFile();
  } else if (item.name === "拍照") {
    checkCameraPermission();
  } else if (item.name === "相册") {
    uploadPicture();
  }
}

async function createNote() {
  const filename = await showAskFilenameDialog("文件", "");
  const timestamp = Date.now();
  const fileMeta = createNewFileMeta(
    timestamp,
    appStore.currentFolderId,
    "file",
    filename,
    0
  );
  await putFileMeta(fileMeta);
  await updateParentFolderFileCount(fileMeta, 1);
}

async function createFolder() {
  const folderName = await showAskFilenameDialog("文件夹", "");
  const timestamp = Date.now();
  const fileMeta = createNewFileMeta(
    timestamp,
    appStore.currentFolderId,
    "folder",
    folderName,
    0
  );
  await putFileMeta(fileMeta);
  await updateParentFolderFileCount(fileMeta, 1);
}

function uploadFile() {
  pdfInputRef.value?.click();
}

function uploadFileChange(event) {
  // 获取文件列表
  const selectedFiles = event.target?.files;
  appStore.currentFileMeta = null;
  if (selectedFiles.length > 0) {
    const file = selectedFiles[0];
    loadPDFFile(file);
  }
}

async function loadPDFFile(file) {
  let loading = ElLoading.service({
    lock: true,
    text: "正在处理PDF",
  });

  try {
    const fileData = await asyncReadAsDataURL(file);
    if (!fileData.data) {
      loading.close();
      ElMessage.error("无法读取PDF文件");
      return;
    }

    const pdfData = atob(fileData.data.split(",")[1]);
    const loadingTask = pdfjsLib.getDocument({ data: pdfData });
    const pdfDoc = await loadingTask.promise;

    // 检查PDF页数
    const totalPages = pdfDoc.numPages;
    let pagesToProcess = totalPages;

    if (totalPages > 10) {
      loading.close();

      if (profileStore.isVIP) {
        // VIP用户：提醒页数过多可能处理缓慢
        const confirmed = await ElMessageBox.confirm(
          `此PDF共有${totalPages}页，页数较多可能处理缓慢，是否继续？`,
          "提示",
          {
            confirmButtonText: "继续处理",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).catch(() => false);

        if (!confirmed) {
          return;
        }
      } else {
        // 非VIP用户：只能处理前10页
        const confirmed = await ElMessageBox.confirm(
          `此PDF共有${totalPages}页，非VIP用户只能处理前10页，是否继续？`,
          "提示",
          {
            confirmButtonText: "处理前10页",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).catch(() => false);

        if (!confirmed) {
          return;
        }

        pagesToProcess = 10;
      }

      // 重新显示loading
      loading = ElLoading.service({
        lock: true,
        text: "正在处理PDF",
      });
    }

    const imageFiles = [];

    // 转换每一页为图片
    for (let i = 1; i <= pagesToProcess; ++i) {
      loading.setText(`正在处理第 ${i}/${pagesToProcess} 页...`);

      const page = await pdfDoc.getPage(i);
      const viewport = page.getViewport({ scale: 1.0 }); // 使用2倍缩放提高清晰度

      // 创建canvas
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      // 渲染PDF页面到canvas
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await page.render(renderContext).promise;

      // 将canvas转换为blob
      const blob = await new Promise((resolve) => {
        canvas.toBlob(resolve, "image/jpeg", 0.9);
      });

      // 创建File对象
      const imageFilename =
        i === 1 ? `${file.name}.jpg` : `${file.name}_page_${i}.jpg`;
      const imageFile = new File([blob], imageFilename, {
        type: "image/jpeg",
      });

      // 添加uid用于标识
      imageFile.uid = Date.now() + i;
      imageFiles.push(imageFile);

      const textContent = await page.getTextContent();

      // 使用单词坐标处理函数获取单词级别的坐标信息
      const wordData = processTextWithWordCoordinates(textContent, viewport);

      // 将单词数据转换为与原textData兼容的格式
      const textData = wordData.map((wordItem, index) => {
        return {
          uid: wordItem.uid,
          text: wordItem.word,
          bbox: wordItem.bbox,
          confidence: wordItem.confidence,
          line: {
            text: wordItem.originalItem.str, // 保留原始行文本
            bbox: {
              x0: wordItem.originalItem.transform[4],
              y0: wordItem.originalItem.transform[5] - wordItem.originalItem.height,
              x1: wordItem.originalItem.transform[4] + wordItem.originalItem.width,
              y1: wordItem.originalItem.transform[5],
            },
            confidence: 100,
            rowAttributes: {
              rowHeight: wordItem.originalItem.height,
            },
          },
          // 添加单词特有的属性
          word: wordItem.word,
          isWord: true, // 标识这是单词级别的数据
        };
      });

      await saveOcrData(imageFile.uid, textData, canvas.width, canvas.height);
    }

    loading.close();

    if (imageFiles.length > 0) {
      // 将转换后的图片文件设置到store中，然后跳转到图片预览页面
      appStore.currentFile = imageFiles;
      appStore.currentFileMeta = null;
      router.push("/app");
    } else {
      ElMessage.error("PDF转换失败");
    }
  } catch (error) {
    loading.close();
    console.error("PDF转换错误:", error);
    ElMessage.error("PDF转换失败: " + error.message);
  }
}

function checkCameraPermission() {
  if (typeof ReactNativeWebView !== "undefined") {
    const data = {
      action: "check-camera-permission",
    };
    // eslint-disable-next-line no-undef
    ReactNativeWebView.postMessage(JSON.stringify(data));
  } else {
    ElMessage.error("无法获取拍照权限");
  }
}

function takePhoto() {
  cameraInputRef.value?.click();
}

function takePhotoChange(event) {
  // 获取文件列表
  const selectedFiles = event.target?.files;
  appStore.currentFileMeta = null;
  if (selectedFiles.length > 0) {
    const files = [];
    for (let i = 0; i < selectedFiles.length; i++) {
      files.push(selectedFiles[i]);
    }
    appStore.currentFile = files;
    router.push("/photo-preview");
  }
}

function uploadPicture() {
  photoInputRef.value?.click();
}

function uploadPictureChange(event) {
  // 获取文件列表
  const selectedFiles = event.target?.files;
  appStore.currentFileMeta = null;
  if (selectedFiles.length > 0) {
    const files = [];
    for (let i = 0; i < selectedFiles.length; i++) {
      files.push(selectedFiles[i]);
    }
    appStore.currentFile = files;
    router.push("/cropper-preview");
  }
}
</script>

<style lang="scss" scoped>
.trial-warning {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  width: 100%;

  div:nth-child(3) {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: 5px;
  }
}
</style>
